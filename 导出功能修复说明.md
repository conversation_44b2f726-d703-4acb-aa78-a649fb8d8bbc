# 导出功能修复说明

## 修复的问题

### 1. 导出的表格数据与数据展示区显示的数据不一致

**问题描述：**
- 在数据采集过程中，有渠道占比过滤功能
- 不符合渠道占比条件的商品不在表格中显示，但仍然被添加到 `collected_data` 中
- 导出时使用的是 `collected_data`，包含了所有数据（包括被过滤掉的）
- 而用户在界面上看到的是经过过滤的数据

**解决方案：**
- 新增 `get_displayed_table_data()` 方法，直接从表格中读取显示的数据
- 修改 `on_export_clicked()` 方法，使用表格中的数据而不是 `collected_data`
- 确保导出的数据与用户看到的数据完全一致

### 2. 导出的文件命名格式修改

**问题描述：**
- 原文件名格式：`快手采集数据_{timestamp}.xlsx`
- 需要改为：`类目+筛选器选择的日期.xlsx`
- 需要处理类目名称中的"/"字符

**解决方案：**

#### 商品采集数据导出
- 新增 `generate_export_filename()` 方法
- 文件名格式：`{类目层级}+{日期范围}.xlsx`
- 类目处理：
  - 按层级顺序组合：一级类目-二级类目-三级类目-四级类目
  - 去除类目名称中的"/"字符，替换为"-"
  - 如果没有选择类目，使用"全部类目"
- 日期处理：
  - 提取筛选器中选择的日期
  - 处理"本周"格式，提取括号内的实际日期
  - 将"-"替换为"至"，使文件名更清晰

#### 查询数据导出
- 新增 `generate_query_export_filename()` 方法
- 文件名格式：`商品成交量查询+{时间范围}+{具体日期范围}.xlsx`
- 根据选择的时间范围（30天/60天/90天）计算具体的日期范围

## 修改的文件

### main.py
1. **修改 `on_export_clicked()` 方法**
   - 使用 `get_displayed_table_data()` 获取表格显示的数据
   - 使用 `generate_export_filename()` 生成文件名

2. **新增 `get_displayed_table_data()` 方法**
   - 直接从数据表格中读取显示的数据
   - 确保导出数据与界面显示一致

3. **新增 `generate_export_filename()` 方法**
   - 根据当前筛选条件生成文件名
   - 处理类目名称中的特殊字符
   - 格式化日期显示

4. **修改 `on_export_query_clicked()` 方法**
   - 使用 `generate_query_export_filename()` 生成文件名

5. **新增 `generate_query_export_filename()` 方法**
   - 为查询数据生成专门的文件名格式

## 文件名示例

### 商品采集数据
- `服饰内衣-女装-女士精品-连衣裙+2024至01至15至2024至01至21.xlsx`
- `3C数码-家电-手机-通讯+2024至01至08至2024至01至14.xlsx`
- `全部类目+本周至2024至01至15至2024至01至21.xlsx`

### 查询数据
- `商品成交量查询+30天+2025-07-04至2025-08-02.xlsx`
- `商品成交量查询+60天+2025-06-04至2025-08-02.xlsx`

## 测试验证

创建了 `test_export_fix.py` 测试脚本，验证：
1. 类目名称处理（包括"/"字符替换）
2. 日期格式处理
3. 文件名生成逻辑
4. 查询数据文件名生成

所有测试均通过，确保修改的正确性。

## 注意事项

1. **向后兼容性**：如果文件名生成失败，会回退到原来的时间戳格式
2. **特殊字符处理**：类目名称中的"/"字符会被替换为"-"，避免文件系统问题
3. **数据一致性**：导出的数据现在与界面显示的数据完全一致
4. **错误处理**：所有新增方法都包含异常处理，确保程序稳定性
